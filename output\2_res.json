{"input_path": "2.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[678, 420], [709, 417], [710, 432], [679, 435]], [[671, 436], [714, 432], [715, 453], [672, 456]], [[529, 451], [627, 455], [626, 485], [528, 480]], [[849, 448], [888, 448], [888, 467], [849, 467]], [[959, 446], [1105, 446], [1105, 463], [959, 463]], [[221, 469], [362, 474], [361, 504], [220, 498]], [[806, 466], [927, 466], [927, 486], [806, 486]], [[959, 462], [1058, 462], [1058, 481], [959, 481]], [[324, 508], [499, 512], [499, 533], [324, 528]], [[301, 529], [502, 534], [501, 579], [300, 574]], [[539, 555], [573, 558], [572, 579], [538, 576]], [[370, 595], [428, 595], [428, 609], [370, 609]], [[217, 613], [352, 616], [352, 633], [217, 630]], [[277, 638], [367, 640], [367, 665], [277, 663]], [[382, 640], [553, 643], [553, 668], [382, 665]], [[804, 644], [844, 644], [844, 667], [804, 667]], [[880, 645], [921, 645], [921, 668], [880, 668]], [[983, 645], [1026, 645], [1026, 669], [983, 669]], [[215, 682], [335, 685], [335, 708], [215, 705]], [[351, 684], [516, 688], [516, 711], [351, 707]], [[238, 719], [293, 719], [293, 737], [238, 737]], [[237, 733], [294, 733], [294, 750], [237, 750]], [[330, 725], [466, 729], [466, 753], [330, 750]], [[508, 729], [545, 729], [545, 749], [508, 749]], [[785, 752], [853, 756], [852, 781], [784, 778]], [[870, 752], [935, 752], [935, 770], [870, 770]], [[252, 780], [459, 782], [459, 803], [252, 801]], [[504, 788], [546, 791], [545, 814], [503, 811]], [[779, 803], [1038, 816], [1037, 840], [778, 827]], [[778, 828], [829, 831], [828, 853], [777, 850]], [[846, 830], [978, 837], [977, 861], [845, 854]], [[991, 837], [1046, 841], [1045, 864], [990, 860]], [[777, 851], [904, 858], [903, 882], [776, 875]], [[926, 861], [956, 861], [956, 882], [926, 882]], [[955, 859], [993, 862], [992, 885], [953, 882]], [[993, 858], [1048, 861], [1047, 891], [992, 888]], [[692, 922], [774, 929], [772, 951], [690, 944]], [[810, 935], [1114, 951], [1113, 974], [809, 958]], [[690, 959], [1156, 988], [1154, 1019], [688, 990]], [[982, 1485], [1056, 1483], [1060, 1584], [986, 1586]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.0, "rec_texts": ["AI", "20", "合格证", "PA", "G57 2150210021", "HOUEy华立", "2021E1023-33", "DL/********", "非限公司", "G948052", "AWh", "", "71729321-2000", "DDS28型", "单相电子式电能表", "有功", "拉", "红外", "220V 10(80）A", "50Hz1600imp/kWh", "共供盘", "2011-06", "表号11225241", "查询", "海思载波", "运本", "54B253C9S1511225241", "编程", "DDZ666-Z型单相智能电能表", "220V", "0.25-0.5（60）A", "50Hz", "2000imp/kWh", "A", "H3", "回", "中国汽电网", "06021DY00000022401781276", "CHNT浙江正泰仪器仪表有限责任公司2025年", "川"], "rec_scores": [0.21432088315486908, 0.31532227993011475, 0.9805439114570618, 0.9162694215774536, 0.5936000943183899, 0.6506776809692383, 0.9927599430084229, 0.8086103200912476, 0.5694681406021118, 0.7536635398864746, 0.5518903732299805, 0.0, 0.6361793875694275, 0.9772592186927795, 0.9926055073738098, 0.9776347875595093, 0.982610285282135, 0.9107770919799805, 0.8337368965148926, 0.805799126625061, 0.3116895854473114, 0.8903579115867615, 0.9963990449905396, 0.8605841398239136, 0.7920564413070679, 0.4611220955848694, 0.9859258532524109, 0.9159663915634155, 0.9652625918388367, 0.9955750703811646, 0.9051206111907959, 0.8970242738723755, 0.9773797988891602, 0.39405474066734314, 0.9855368137359619, 0.5652056932449341, 0.8307758569717407, 0.9974238276481628, 0.9668318033218384, 0.7737624049186707], "rec_polys": [[[678, 420], [709, 417], [710, 432], [679, 435]], [[671, 436], [714, 432], [715, 453], [672, 456]], [[529, 451], [627, 455], [626, 485], [528, 480]], [[849, 448], [888, 448], [888, 467], [849, 467]], [[959, 446], [1105, 446], [1105, 463], [959, 463]], [[221, 469], [362, 474], [361, 504], [220, 498]], [[806, 466], [927, 466], [927, 486], [806, 486]], [[959, 462], [1058, 462], [1058, 481], [959, 481]], [[324, 508], [499, 512], [499, 533], [324, 528]], [[301, 529], [502, 534], [501, 579], [300, 574]], [[539, 555], [573, 558], [572, 579], [538, 576]], [[370, 595], [428, 595], [428, 609], [370, 609]], [[217, 613], [352, 616], [352, 633], [217, 630]], [[277, 638], [367, 640], [367, 665], [277, 663]], [[382, 640], [553, 643], [553, 668], [382, 665]], [[804, 644], [844, 644], [844, 667], [804, 667]], [[880, 645], [921, 645], [921, 668], [880, 668]], [[983, 645], [1026, 645], [1026, 669], [983, 669]], [[215, 682], [335, 685], [335, 708], [215, 705]], [[351, 684], [516, 688], [516, 711], [351, 707]], [[238, 719], [293, 719], [293, 737], [238, 737]], [[237, 733], [294, 733], [294, 750], [237, 750]], [[330, 725], [466, 729], [466, 753], [330, 750]], [[508, 729], [545, 729], [545, 749], [508, 749]], [[785, 752], [853, 756], [852, 781], [784, 778]], [[870, 752], [935, 752], [935, 770], [870, 770]], [[252, 780], [459, 782], [459, 803], [252, 801]], [[504, 788], [546, 791], [545, 814], [503, 811]], [[779, 803], [1038, 816], [1037, 840], [778, 827]], [[778, 828], [829, 831], [828, 853], [777, 850]], [[846, 830], [978, 837], [977, 861], [845, 854]], [[991, 837], [1046, 841], [1045, 864], [990, 860]], [[777, 851], [904, 858], [903, 882], [776, 875]], [[926, 861], [956, 861], [956, 882], [926, 882]], [[955, 859], [993, 862], [992, 885], [953, 882]], [[993, 858], [1048, 861], [1047, 891], [992, 888]], [[692, 922], [774, 929], [772, 951], [690, 944]], [[810, 935], [1114, 951], [1113, 974], [809, 958]], [[690, 959], [1156, 988], [1154, 1019], [688, 990]], [[982, 1485], [1056, 1483], [1060, 1584], [986, 1586]]], "rec_boxes": [[678, 417, 710, 435], [671, 432, 715, 456], [528, 451, 627, 485], [849, 448, 888, 467], [959, 446, 1105, 463], [220, 469, 362, 504], [806, 466, 927, 486], [959, 462, 1058, 481], [324, 508, 499, 533], [300, 529, 502, 579], [538, 555, 573, 579], [370, 595, 428, 609], [217, 613, 352, 633], [277, 638, 367, 665], [382, 640, 553, 668], [804, 644, 844, 667], [880, 645, 921, 668], [983, 645, 1026, 669], [215, 682, 335, 708], [351, 684, 516, 711], [238, 719, 293, 737], [237, 733, 294, 750], [330, 725, 466, 753], [508, 729, 545, 749], [784, 752, 853, 781], [870, 752, 935, 770], [252, 780, 459, 803], [503, 788, 546, 814], [778, 803, 1038, 840], [777, 828, 829, 853], [845, 830, 978, 861], [990, 837, 1046, 864], [776, 851, 904, 882], [926, 861, 956, 882], [953, 859, 993, 885], [992, 858, 1048, 891], [690, 922, 774, 951], [809, 935, 1114, 974], [688, 959, 1156, 1019], [982, 1483, 1060, 1586]]}