import cv2
import matplotlib.pyplot as plt
import numpy as np
import os

# 创建输出目录
if not os.path.exists('processed_images'):
    os.makedirs('processed_images')

# Load the image
image = cv2.imread('1.jpg')

# Convert the image to grayscale
gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# 1. 应用高斯模糊去噪
blurred = cv2.GaussianBlur(gray_image, (3, 3), 0)

# 2. 增强对比度 - CLAHE (Contrast Limited Adaptive Histogram Equalization)
clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
enhanced = clahe.apply(blurred)

# 3. 优化的自适应阈值参数
adaptive_thresh = cv2.adaptiveThreshold(
    enhanced,         # 增强后的灰度图像
    255,              # Maximum value
    cv2.ADAPTIVE_THRESH_GAUSSIAN_C,  # 使用高斯权重，比均值更好
    cv2.THRESH_BINARY,  # Thresholding type (binary)
    15,               # 增大块大小以获得更平滑的结果
    4                 # 增大常数以减少噪声
)

# 4. 形态学操作 - 去除小噪点并连接断开的字符
kernel = np.ones((2,2), np.uint8)
# 先腐蚀去除小噪点
eroded = cv2.erode(adaptive_thresh, kernel, iterations=1)
# 再膨胀恢复字符大小
final_processed = cv2.dilate(eroded, kernel, iterations=1)

# 保存处理后的图片
cv2.imwrite('processed_images/enhanced_grayscale.jpg', enhanced)
cv2.imwrite('processed_images/adaptive_threshold.jpg', adaptive_thresh)
cv2.imwrite('processed_images/final_processed.jpg', final_processed)

# Show the processing steps
plt.figure(figsize=(20, 12))

# Original image
plt.subplot(2, 4, 1)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.title('1. Original Image')
plt.axis('off')

# Grayscale
plt.subplot(2, 4, 2)
plt.imshow(gray_image, cmap='gray')
plt.title('2. Grayscale')
plt.axis('off')

# Blurred
plt.subplot(2, 4, 3)
plt.imshow(blurred, cmap='gray')
plt.title('3. Gaussian Blur')
plt.axis('off')

# Enhanced contrast
plt.subplot(2, 4, 4)
plt.imshow(enhanced, cmap='gray')
plt.title('4. CLAHE Enhanced')
plt.axis('off')

# Adaptive thresholded image
plt.subplot(2, 4, 5)
plt.imshow(adaptive_thresh, cmap='gray')
plt.title('5. Adaptive Threshold')
plt.axis('off')

# Eroded
plt.subplot(2, 4, 6)
plt.imshow(eroded, cmap='gray')
plt.title('6. Erosion')
plt.axis('off')

# Final processed
plt.subplot(2, 4, 7)
plt.imshow(final_processed, cmap='gray')
plt.title('7. Final Processed')
plt.axis('off')

# 显示数字区域的放大图
plt.subplot(2, 4, 8)
# 假设数字在图像中央区域，裁剪显示
h, w = final_processed.shape
roi = final_processed[h//3:2*h//3, w//4:3*w//4]
plt.imshow(roi, cmap='gray')
plt.title('8. ROI - Number Area')
plt.axis('off')

plt.tight_layout()
plt.show()

print("处理完成！图片已保存到 'processed_images' 文件夹中：")
print("- enhanced_grayscale.jpg: 对比度增强后的灰度图")
print("- adaptive_threshold.jpg: 自适应阈值处理后的图")
print("- final_processed.jpg: 最终处理后的图片（推荐用于OCR识别）")
